import { useState } from "react";
import { useForm } from "react-hook-form";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { SearchableSelect, SearchableSelectOption } from "@/components/ui/searchable-select";

import { useFundingRounds } from "@/hooks/use-funding-rounds";
import { useToast } from "@/components/ui/use-toast";
import { Loading } from "@/components/ui/loading";
import { useAttachFundingRoundDialog } from "@/hooks/use-attach-funding-round-dialog";

type FormData = {
  fundingRoundId: string;
};

export function AttachToFundingRoundDialog() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { isOpen, projectData, closeDialog } = useAttachFundingRoundDialog();

  // Fetch funding rounds data
  const {
    data: fundingRounds,
    isLoading: isFundingRoundsLoading,
    error: fundingRoundsError,
  } = useFundingRounds();
  console.log("🚀 ~ AttachToFundingRoundDialog ~ fundingRounds:", fundingRounds)

  // Form setup
  const {
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      fundingRoundId: "",
    },
  });

  const selectedFundingRoundId = watch("fundingRoundId");

  // Convert funding rounds to searchable select options
  const fundingRoundOptions: SearchableSelectOption[] =
    fundingRounds?.content?.map((round) => ({
      value: round.id,
      label: `Round ${round.roundNumber} (${new Date(round.openDate).toLocaleDateString()} - ${new Date(round.closeDate).toLocaleDateString()})`,
    })) ?? [];

  const onSubmit = async (data: FormData) => {
    if (!data.fundingRoundId) {
      toast({
        title: "Error",
        description: "Please select a funding round.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // TODO: Implement the actual API call to attach project to funding round
      // This would typically be a POST request to an endpoint like:
      // POST /projects/{projectId}/attach-funding-round
      // with body: { fundingRoundId: data.fundingRoundId }

      console.log("Attaching project to funding round:", {
        projectId: projectData?.id,
        projectName: projectData?.name,
        fundingRoundId: data.fundingRoundId,
      });

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: "Success",
        description: `Project "${projectData?.name}" has been attached to the selected funding round.`,
      });

      closeDialog();
    } catch (error) {
      console.error("Error attaching project to funding round:", error);
      toast({
        title: "Error",
        description: "Failed to attach project to funding round. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    closeDialog();
  };

  return (
    <Dialog open={isOpen} onOpenChange={closeDialog}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Attach to Funding Round</DialogTitle>
          <DialogDescription>
            Select a funding round to attach the project "{projectData?.name}" to.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fundingRound">Funding Round</Label>
            {isFundingRoundsLoading ? (
              <Loading message="Loading funding rounds..." />
            ) : fundingRoundsError ? (
              <div className="text-sm text-destructive">
                Error loading funding rounds. Please try again.
              </div>
            ) : (
              <SearchableSelect
                options={fundingRoundOptions}
                value={selectedFundingRoundId}
                onValueChange={(value) => setValue("fundingRoundId", value)}
                placeholder="Select a funding round..."
                searchPlaceholder="Search funding rounds..."
                emptyMessage="No funding rounds found."
                disabled={isSubmitting}
              />
            )}
            {errors.fundingRoundId && (
              <div className="text-sm text-destructive">
                {errors.fundingRoundId.message}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !selectedFundingRoundId}
            >
              {isSubmitting ? "Attaching..." : "Attach"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
